from constants import (
    BED<PERSON><PERSON>K_EMBEDDING_MODEL_ID,
    B<PERSON><PERSON><PERSON><PERSON>_LLM_MODEL_ID,
    SUMMARY_TEMPERATURE,
    RAG_SYSTEM_PROMPT,
    MAX_POOL_CONNECTIONS_AWS_CLIENT,
    AWS_BEDROCK_MAX_RETRIES,
)
from services.custom_chat_bedrock_converse import CustomChatBedrockConverse
import aioboto3
from aiobotocore.config import AioConfig
import logging
from utils.enums import OCRType, CloudPlatform
from services.embeddings.custom_bedrock_embeddings import CustomBedrockEmbeddings
from typing import Type, AsyncGenerator, List, Tuple, Dict, Any
from pydantic import BaseModel
from langchain.output_parsers import PydanticToolsParser
from langchain_community.callbacks.manager import get_bedrock_anthropic_callback
from langchain_core.prompts import ChatPromptTemplate
from contextlib import asynccontextmanager


class AWSHelper:
    """
    A helper class that interacts with AWS services to perform tasks
    like generating responses, and running OCR.
    """

    def __init__(self):
        """
        Initializes the AWSHelper class with the cloud name.
        """
        self.cloud_name = CloudPlatform.aws

    @asynccontextmanager
    async def get_chat_model(
        self, llm_reasoning_enabled: str = "false"
    ) -> AsyncGenerator[CustomChatBedrockConverse, None]:
        """
        Method to yield the CustomChatBedrockConverse chat model.
        Args:
            llm_reasoning_enabled (str): Whether to enable LLM reasoning.
        """
        session = aioboto3.Session()
        async with session.client(
            "bedrock-runtime",
            config=AioConfig(
                max_pool_connections=MAX_POOL_CONNECTIONS_AWS_CLIENT,
                retries={
                    "max_attempts": AWS_BEDROCK_MAX_RETRIES,
                    "mode": "standard",
                },
            ),
        ) as client:
            model = CustomChatBedrockConverse(
                client=client,
                model_id=BEDROCK_LLM_MODEL_ID,
                temperature=SUMMARY_TEMPERATURE,
                max_tokens=4096,
            )
            yield model

    async def get_llm_response_with_metadata(
        self,
        query_prompt: str,
        response_format: str = "text",
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> Dict[str, Any]:
        """
        Generates a response from the AWS Bedrock model based on the provided query prompt.
        Args:
            query_prompt (str): The user's query.
            response_format (str): The format of the response (default is 'text').
            system_prompt (str): System prompt to guide the AI model's behavior.
            llm_reasoning_enabled (str): Whether to enable LLM reasoning.
        Returns:
            Dict[str, Any]: A dictionary containing the response content and token usage.
        """
        messages = [
            (
                "system",
                system_prompt,
            ),
            ("human", query_prompt),
        ]

        async with self.get_chat_model(llm_reasoning_enabled) as chat_model:
            ai_msg = await chat_model.ainvoke(messages)

        response = {
            "content": ai_msg.content,
            "total_tokens": ai_msg.usage_metadata["total_tokens"],
            "prompt_tokens": ai_msg.usage_metadata["input_tokens"],
            "completion_tokens": ai_msg.usage_metadata["output_tokens"],
        }

        return response

    async def get_llm_pydantic_schema_response_with_metadata(
        self,
        question_prompt: str,
        output_pydantic_schema: Type[BaseModel],
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> Dict[str, Any]:
        """
        Generates a response from Azure OpenAI based on the provided query prompt.
        Args:
            - question_prompt (str): The user's query.
            - output_pydantic_schema (str): The output pydantic schema.
            - system_prompt (str): System prompt to guide the AI model's behavior.
        Returns:
            - Dict[str, Any]: A dictionary containing the response content and token usage.
        """
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("human", "{question_prompt}"),
            ]
        )
        async with self.get_chat_model(llm_reasoning_enabled) as chat_model:
            llm_with_tools = chat_model.bind_tools([output_pydantic_schema])
            query_analyzer = (
                prompt
                | llm_with_tools
                | PydanticToolsParser(tools=[output_pydantic_schema])
            )

            with get_bedrock_anthropic_callback() as callback:
                llm_response = await query_analyzer.ainvoke(
                    {"question_prompt": question_prompt}
                )
                tokens_usage = {
                    "total_tokens": callback.total_tokens,
                    "prompt_tokens": callback.prompt_tokens,
                    "completion_tokens": callback.completion_tokens,
                    "total_cost": callback.total_cost,
                }

            return {"llm_response": llm_response, "tokens_usage": tokens_usage}

    @asynccontextmanager
    async def get_embedding_model(
        self,
    ) -> AsyncGenerator[CustomBedrockEmbeddings, None]:
        """
        Method to yield the CustomBedrockEmbeddings embedding model.
        """
        session = aioboto3.Session()
        async with session.client(
            "bedrock-runtime",
            config=AioConfig(max_pool_connections=MAX_POOL_CONNECTIONS_AWS_CLIENT),
        ) as client:
            embeddings = CustomBedrockEmbeddings(
                client=client,
                model_id=BEDROCK_EMBEDDING_MODEL_ID,
            )
            yield embeddings

    async def embed_documents(self, texts: List[str]) -> Dict[str, Any]:
        """
        Embeds a list of texts.
        Args:
            texts (List[str]): The texts to embed.
        Returns:
            Dict[str, Any]: A dictionary containing the embeddings and usage.
        """
        async with self.get_embedding_model() as embedding_model:
            return await embedding_model.aembed_documents(texts)

    async def embed_query(self, query: str) -> Tuple[List[float], Dict[str, Any], str]:
        """
        Embeds a query.
        Args:
            query (str): The query to embed.
        Returns:
            Tuple[List[float], Dict[str, Any], str]: A tuple containing the embeddings, usage, and model.
        """
        async with self.get_embedding_model() as embedding_model:
            response = await embedding_model.aembed_query(query)
            return response["embeddings"][0], response["usage"], response["model"]

    async def get_extracted_text_from_image(
        self, image: bytes, page_no: str, image_width: int, image_height: int
    ) -> Tuple[Dict[str, Any], str]:
        """
        This function sends the image data to Amazon Textract to extract text from the image.
        Args:
            - image: Byte data of the image to be processed.
            - page_no: The page number corresponding to the image.
            - image_width: The width of the image.
            - image_height: The height of the image.
        Returns:
            - Tuple[Dict[str, Any], str]: A tuple containing the OCR result and the page number.
        """
        session = aioboto3.Session()
        async with session.client(
            "textract",
            config=AioConfig(max_pool_connections=MAX_POOL_CONNECTIONS_AWS_CLIENT),
        ) as textract_client:
            ocr_reponse = await textract_client.detect_document_text(
                Document={"Bytes": image}
            )
            ocr_reponse["image_size"] = {
                "width": int(image_width),
                "height": int(image_height),
            }
            return (ocr_reponse, page_no)

    async def get_formatted_ocr_response(
        self, document_text_list: Tuple[Dict[str, Any], str]
    ) -> Dict[str, Any]:
        """
        This function formats the raw document_text_list from Amazon Textract into a structured JSON format.
        Args:
            - document_text_list: Tuple of OCR results from Textract, with each item containing OCR data and page number.
        Returns:
            - Dict[str, Any]: A dictionary with formatted OCR results, including the content, bounding box info, and image size.
        """
        document_text_json = {}
        for response in document_text_list:
            ocr_response = response[0]
            page_no = response[1]

            document_text_json[page_no] = {
                "content": "",
                "info": [],
                "image_size": {},
                "angle": 0,
                "extraction": OCRType.AWS,
            }
            if "Blocks" not in ocr_response:
                continue

            for block in ocr_response["Blocks"]:
                if "LINE" == block.get("BlockType"):
                    document_text_json[page_no]["content"] += " " + block["Text"]
                if "WORD" == block.get("BlockType"):
                    pixels = await self._extract_pixels(
                        block,
                        ocr_response["image_size"]["width"],
                        ocr_response["image_size"]["height"],
                    )
                    document_text_json[page_no]["info"].append(
                        [
                            pixels,
                            block["Text"],
                        ]
                    )
            document_text_json[page_no]["image_size"] = ocr_response["image_size"]
        return document_text_json

    async def _extract_pixels(
        self, block: Dict[str, Any], image_width: int, image_height: int
    ) -> List[int]:
        """
        This private function extracts pixel coordinates of the bounding box for a block of text detected by Textract.
        Args:
            - block: A block object returned from Textract that contains bounding box info.
            - image_width: The width of the image being processed.
            - image_height: The height of the image being processed.
        Returns:
            - List[int]: A list of pixel coordinates corresponding to the block's bounding box.
        """
        points = None
        for coordinate in block["Geometry"]["Polygon"]:
            if not points:
                points = [
                    coordinate["X"],
                    coordinate["Y"],
                    coordinate["X"],
                    coordinate["Y"],
                ]
            else:
                points[0] = min(points[0], coordinate["X"])  # x1
                points[1] = min(points[1], coordinate["Y"])  # y1
                points[2] = max(points[2], coordinate["X"])  # x2
                points[3] = max(points[3], coordinate["Y"])  # y2

        # Converting coordinate to pixels
        pixels = []
        pixels.append(int(points[0] * image_width))
        pixels.append(int(points[1] * image_height))
        pixels.append(int(points[2] * image_width))
        pixels.append(int(points[3] * image_height))
        return pixels
