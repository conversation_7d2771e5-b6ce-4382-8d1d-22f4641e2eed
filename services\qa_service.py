from fastapi import Request
from typing import Optional, List, Dict, Any
from services.endpoint_handlers.rag_service import RetrievalAugmentedGeneration
from utils.document_utils import SourceDocument
from constants import RAG_ENABLED, DEFAULT_LLM_REASONING_ENABLED


async def get_answer_with_context(
    request: Request,
    questions: List[str],
    source_document: SourceDocument,
    qa_prompt: str,
    llm_platform: str,
    embedding_platform: str,
    default_fields: Optional[List[str]] = ["Height||in", "Weight||lbs"],
    rag_enabled: str = RAG_ENABLED,
    llm_reasoning_enabled: str = DEFAULT_LLM_REASONING_ENABLED,
) -> Dict[str, Any]:
    """Create embeddings and answer asked Question.
    Args:
        request (Request): The request object.
        questions (List[str]): Asked user questions.
        source_document: SourceDocument:  The document text or json.
        qa_prompt (str): The prompt for the LLM.
        llm_platform (str): The platform for the LLM.
        embedding_platform (str): The platform for the embedding.
        default_fields (str, optional): The default fields to extract. Default is "Height (inches), Weight (pounds)".
    Returns:
        Dict[str, Any]: A dictionary containing the answer for the asked user question.
    """

    rag_service = RetrievalAugmentedGeneration(
        request,
        llm_reasoning_enabled=llm_reasoning_enabled,
    )
    await rag_service.initialize(
        llm_platform,
        embedding_platform,
        source_document,
        rag_enabled=rag_enabled,
    )
    answers = await rag_service.get_answer_with_context(
        questions, qa_prompt, default_fields, rag_enabled=rag_enabled
    )
    return answers
