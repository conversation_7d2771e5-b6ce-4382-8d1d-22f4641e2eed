import json
from typing import Any, Dict, List


async def convert_text_to_dict(text):
    cleaned_text = text.replace("```json\n", "").replace("\n```", "")
    try:
        return json.loads(cleaned_text)
    except:
        return eval(cleaned_text)


async def post_process_data_points(
    answers: Dict[str, Any], data_points: List[str]
) -> Dict[str, Any]:
    """
    Post process data points.
    Args:
        answers (Dict[str, Any]): The answers.
        data_points (List[str]): The data points.
    Returns:
        Dict[str, Any]: The post processed data points.
    """
    updated_answers = {
        data_point: (
            answers[data_point]
            if data_point in answers
            and answers[data_point] is not None
            and "None" != answers[data_point]
            else ""
        )
        for data_point in data_points
    }
    updated_answers["Chunks"] = answers.get("Chunks")
    return updated_answers
