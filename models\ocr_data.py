from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, RootModel
import json
import logging
from exceptions import ValidationError
from constants import DEFAULT_OCR_TYPE


class ImageSize(BaseModel):
    """Model for image size information."""

    width: int
    height: int


class UsageCost(BaseModel):
    """Model for usage cost information."""

    ocrCost: float = 0
    llmCost: float = 0
    embeddingCost: float = 0


class PageContent(BaseModel):
    """Model for a single page content in OCR data."""

    content: str = Field(..., description="The content of the page.")
    info: List[List[Any]] = Field(
        ..., description="The word level bounding box information of the page."
    )
    image_size: ImageSize
    angle: Optional[float] = Field(
        None, description="The angle of the page, if available."
    )
    extraction: Optional[str] = Field(
        DEFAULT_OCR_TYPE,
        description="The type of OCR used to extract the page content.",
    )
    annotations: Optional[List[Any]] = Field(
        [], description="The annotations of the page."
    )


class OCRData(RootModel[Dict[str, PageContent]]):
    """
    Model for validating OCR data format.
    """

    @classmethod
    async def validate_ocr_data(cls, data: Dict[str, Any]):
        """
        Validate OCR data from either a JSON string or a dictionary.
        Args:
            data: OCR data as either a JSON string or a dictionary
        Returns:
            Dict[str, PageContent]: Validated OCR data
        Raises:
            ValidationError: If the data doesn't match the expected format.
        """
        logging.info("Validating the OCR data.")
        try:
            usage_cost = None
            if "usageCost" in data:
                usage_cost = data.pop("usageCost")

            validated_data = {}
            pages_text = ""
            for page_num, page_content in data.items():
                if "usageCost" in page_num:
                    UsageCost(**page_content).model_dump()
                    continue

                pages_text += page_content.get("content", "")
                validated_data[page_num] = PageContent(**page_content).model_dump()

            if len(pages_text.strip()) == 0:
                raise ValidationError(
                    "OCR data is empty. Please pass file with some text."
                )

            if usage_cost is not None:
                validated_data["usageCost"] = UsageCost(**usage_cost).model_dump()
            logging.info("OCR JSON validated successfully.")
            return validated_data
        except (json.JSONDecodeError, ValueError) as e:
            logging.error(f"Error validating OCR data: {e}")
            raise ValidationError(f"Invalid ocr_data JSON format: {str(e)}")
        except Exception as e:
            logging.exception(f"Error validating OCR data: {e}")
            raise ValidationError(f"Invalid OCR data format: {str(e)}")
