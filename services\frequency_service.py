from rapidfuzz import fuzz
import logging
import asyncio
from typing import Dict, Any, <PERSON>, Tuple


async def get_keyword_count(
    document_json: Dict[str, Any], query: str
) -> Dict[str, Any]:
    """
    Get the keyword count.
    Args:
        document_json (Dict[str, Any]): The document text JSON.
        query (str): The query.
    Returns:
        Dict[str, Any]: The keyword count.
    """
    keywords = query.split(",")
    logging.info(f"keywords: {keywords}")

    # Remove :,- from the keywords
    keywords = [
        keyword.strip().lower().translate(str.maketrans("", "", ":,-"))
        for keyword in keywords
    ]

    document_json = await split_box_text_into_words(document_json)

    freqs = {}

    freqs["keywords"] = await count_keyword_occurrences(document_json, keywords)

    return freqs


async def count_keywords_in_page(
    keywords: List,
    data: List,
    page_num: int,
    keywords_freq: Dict[str, Any],
    page_data: Dict[str, Any],
    matching_threshold: int = 88,
) -> Dict[str, Any]:
    """
    Count the keyword occurrences in a page.
    Args:
        keywords (List): The keywords.
        data (List): The data.
        page_num (int): The page number.
        keywords_freq (Dict[str, Any]): The keyword frequency.
        page_data (Dict[str, Any]): The page data.
        matching_threshold (int): The matching threshold. Default is 88.
    Returns:
        Dict[str, Any]: The keyword frequency.
    """

    async def match_keyword(keyword: str):
        subsets = await create_subset_with_word_window(data, len(keyword.split()))
        for subset in subsets:
            subset[1] = subset[1].translate(str.maketrans("", "", ":,-"))
            similarity_score = fuzz.ratio(keyword, subset[1].strip())
            if similarity_score > matching_threshold:
                if keyword not in keywords_freq:
                    keywords_freq[keyword] = {
                        "Counts": 1,
                        "Pages": {
                            page_num: {
                                "Counts": 1,
                                "Boxes": [subset[0]],
                                "page_data": page_data,
                            }
                        },
                    }
                else:
                    if page_num not in keywords_freq[keyword]["Pages"]:
                        keywords_freq[keyword]["Counts"] += 1
                        keywords_freq[keyword]["Pages"][page_num] = {
                            "Counts": 1,
                            "Boxes": [subset[0]],
                            "page_data": page_data,
                        }
                    else:
                        result = await check_if_box_overlap(
                            keywords_freq[keyword]["Pages"][page_num]["Boxes"],
                            subset[0],
                        )
                        if not result[0]:
                            keywords_freq[keyword]["Counts"] += 1
                            keywords_freq[keyword]["Pages"][page_num]["Counts"] += 1
                            keywords_freq[keyword]["Pages"][page_num]["Boxes"].append(
                                subset[0]
                            )

    await asyncio.gather(*(match_keyword(k) for k in keywords))
    return keywords_freq


async def count_keyword_occurrences(
    document_json: Dict[str, Any], keywords: List, matching_threshold: int = 88
) -> Dict[str, Any]:
    """
    Count the keyword occurrences across all pages.
    Args:
        document_json (Dict[str, Any]): The document text JSON.
        keywords (List): The keywords.
        matching_threshold (int): The matching threshold. Default is 88.
    Returns:
        Dict[str, Any]: The keyword count.
    """
    keywords_freq: Dict[str, Any] = {}

    async def process_page(page_num):
        nonlocal keywords_freq
        if "usageCost" in page_num:
            return
        data = document_json[page_num]["info"]
        page_data = {
            "image_size": document_json[page_num]["image_size"],
            "angle": document_json[page_num]["angle"],
            "extraction": document_json[page_num]["extraction"],
        }
        await count_keywords_in_page(
            keywords, data, page_num, keywords_freq, page_data, matching_threshold
        )

    await asyncio.gather(*(process_page(page_num) for page_num in document_json))

    for kw in keywords:
        if kw not in keywords_freq:
            keywords_freq[kw] = {"Counts": 0, "Pages": None}

    return keywords_freq


async def check_if_box_overlap(subsets: List, subset: List) -> Tuple:
    """
    Check if the subset overlaps with any of the subsets in the list.
    Args:
        subsets (List): The subsets.
        subset (List): The subset.
    Returns:
        Tuple: The result, the smaller box, and the matched box.
    """
    res = False
    smaller_box = None
    matched_box = None
    for ss in subsets:
        if await check_overlap(ss, subset) == True:
            smaller_box = await pick_smaller_box(ss, subset)
            matched_box = ss
            res = True
            break
    return (res, smaller_box, matched_box)


async def pick_smaller_box(box1: List, box2: List) -> List:
    """
    Pick the smaller box among two given bounding boxes.
    Args:
        box1 (List): List containing the coordinates of the first box in the format [x1, y1, x2, y2].
        box2 (List): List containing the coordinates of the second box in the format [x1, y1, x2, y2].

    Returns:
        List: The smaller box.
    """
    # Calculate the areas of the boxes
    area_box1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area_box2 = (box2[2] - box2[0]) * (box2[3] - box2[1])

    # Compare the areas and return the smaller box
    if area_box1 < area_box2:
        return box1
    else:
        return box2


async def check_overlap(box1: List, box2: List) -> bool:
    """
    Check if two boxes overlap.

    Args:
        box1 (List): List containing the coordinates of the first box in the format [x1, y1, x2, y2].
        box2 (List): List containing the coordinates of the second box in the format [x1, y1, x2, y2].

    Returns:
        bool: True if the boxes overlap, False otherwise.
    """

    return box1 == box2


async def create_subset_with_word_window_without_boxes(
    raw_text: str, window_size: int
) -> List:
    """
    Create subsets with word window without boxes.
    Args:
        raw_text (str): The raw text.
        window_size (int): The window size.
    Returns:
        List: The subsets with word window.
    """
    subsets = []
    # Tokenize the text into words
    words = [i.lower() for i in raw_text.split(" ")]
    # Generate subsets of k words with a sliding window of 1
    subsets_of_k = [
        words[i : i + window_size] for i in range(len(words) - (window_size - 1))
    ]
    # Print the generated subsets
    for subset in subsets_of_k:
        subsets.append(" ".join(subset))
    return subsets


async def create_subset_with_word_window(
    bounding_box_text_list: List, window_size: int
) -> List:
    """
    Create subsets with word window.
    Args:
        bounding_box_text_list (List): The bounding box text List.
        window_size (int): The window size.
    Returns:
        List: The subsets with word window.
    """
    subset_list = []
    for i in range(len(bounding_box_text_list) - window_size + 1):
        subset_text = " ".join(
            bounding_box_text_list[i][1] for i in range(i, i + window_size)
        ).strip()
        subset_box = [bounding_box_text_list[i][0] for i in range(i, i + window_size)]
        if window_size > 1:
            left_most = min(subset_box[0][0], subset_box[1][0])
            top_most = min(subset_box[0][1], subset_box[1][1])
            right_most = max(subset_box[0][2], subset_box[1][2])
            bottom_most = max(subset_box[0][3], subset_box[1][3])
            subset_list.append(
                [
                    [left_most, top_most, right_most, bottom_most],
                    subset_text.lower().strip(),
                ]
            )
        else:
            subset_list.append([subset_box[0], subset_text.lower().strip()])
    return subset_list


async def split_box_text_into_words(document_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    Split the box text into words.
    Args:
        document_json (Dict[str, Any]): The JSON OCR.
    Returns:
        Dict[str, Any]: The JSON OCR with the box text split into words.
    """
    for page_num in document_json:
        if "usageCost" in page_num:
            continue
        temp_tuples = []
        if document_json[page_num]["info"] != {}:
            for bb in document_json[page_num]["info"]:
                word_texts = [t for t in bb[1].strip().split(" ")]
                for w_t in word_texts:
                    temp_tuples.append((bb[0], w_t))
            document_json[page_num]["info"] = temp_tuples
    return document_json
