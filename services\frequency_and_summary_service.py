from services.frequency_service import get_keyword_count
from services.summarization_service import summarize
from utils.document_utils import SourceDocument
from fastapi import Request
from utils.enums import SummaryMethod
from services.summarization_service import SummarizationPrompts
import asyncio
from typing import Dict, Any, List


async def get_frequency_and_summary(
    request: Request,
    document_text_json: Dict[str, Any],
    keywords: List[str],
    system_template: str,
    summary_prompts: SummarizationPrompts,
    summary_method: SummaryMethod,
    llm_platform: str,
    llm_reasoning_enabled: str = "false",
) -> Dict[str, Any]:
    """
    Get the frequency and summary of the document.
    Args:
        request (Request): The request object.
        document_text_json (Dict[str, Any]): The document text JSON.
        keywords (List[str]): The keywords to count frequency.
        system_template (str): The system template.
        summary_prompts (PromptDetails): The summary prompts.
        summary_method (SummaryMethod): The summary method.
        llm_platform (str): The LLM platform.
        llm_reasoning_enabled (str, optional): Whether to enable LLM reasoning. Default is "false".
    Returns:
        Dict[str, Any]: The frequency and summary of the document.
    """
    frequency_and_summary = [
        get_keyword_count(document_text_json, keywords),
        summarize(
            request,
            SourceDocument(document_json=document_text_json),
            system_template,
            summary_prompts,
            llm_platform,
            summary_method,
            llm_reasoning_enabled,
        ),
    ]
    freqs, summary = await asyncio.gather(*frequency_and_summary)
    return {"frequencies": freqs, "summary": summary}
