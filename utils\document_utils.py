from typing import TypedDict, Dict, Any


class SourceDocument(TypedDict):
    document_json: Dict[str, Any]
    document_text: str


async def calculate_pdf_word_count(source_document: SourceDocument) -> int:
    """
    Calculates the pdf word count.
    Args:
        source_document: (SourceDocument): The document text or json.
    Returns:
        int: The pdf word count.
    """
    return (
        len(source_document.get("document_text").split())
        if source_document.get("document_text")
        else sum(
            len(doc.get("content", "").split())
            for doc in source_document.get("document_json").values()
        )
    )
