import json
import logging
import os
from typing import Any, Dict, List
from langchain_aws import BedrockEmbeddings
import asyncio
import numpy as np


class CustomBedrockEmbeddings(BedrockEmbeddings):

    def _embedding_func(
        self, text: str, input_type: str = "search_document"
    ) -> List[float]:
        """Call out to Bedrock embedding endpoint with a single text."""
        # replace newlines, which can negatively affect performance.
        text = text.replace(os.linesep, " ")

        if self.provider == "cohere":
            # Cohere input_type depends on usage
            # for embedding documents use "search_document"
            # for embedding queries for retrieval use "search_query"
            response_body = self._invoke_model(
                input_body={
                    "input_type": input_type,
                    "texts": [text],
                }
            )
            embedding = response_body.get("embeddings")[0]
            token_count = 0
        else:
            # includes common provider == "amazon"
            response_body = self._invoke_model(
                input_body={"inputText": text},
            )
            embedding = response_body.get("embedding")
            token_count = response_body.get("inputTextTokenCount", 0)

            # Prepare the final response with embedding, model ID, and usage details
            result = {
                "embeddings": [embedding],
                "model": self.model_id,
                "usage": {"prompt_tokens": token_count, "total_tokens": token_count},
            }

            return result

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Compute doc embeddings using a Bedrock model.

        Args:
            texts: The list of texts to embed

        Returns:
            List of embeddings, one for each text.
        """

        # If we are able to make use of Cohere's multiple embeddings, use that
        if self.provider == "cohere":
            return self._embed_cohere_documents(texts)
        else:
            return self._iteratively_embed_documents(texts)

    def _embed_cohere_documents(self, texts: List[str]) -> List[List[float]]:
        response = self._cohere_multi_embedding(texts)

        if self.normalize:
            response = [self._normalize_vector(embedding) for embedding in response]

        return response

    def _iteratively_embed_documents(self, texts: List[str]) -> List[List[float]]:
        combined_embeddings = []
        total_prompt_tokens = 0
        total_total_tokens = 0
        model_id = self.model_id
        for text in texts:
            response = self._embedding_func(text)

            if self.normalize:
                response["embeddings"][0] = self._normalize_vector(
                    response["embeddings"][0]
                )

            # Add the embeddings to the combined list
            combined_embeddings.extend(response["embeddings"])

            # Sum the tokens
            total_prompt_tokens += response["usage"]["prompt_tokens"]
            total_total_tokens += response["usage"]["total_tokens"]

        # Return a single combined response
        combined_response = {
            "embeddings": combined_embeddings,
            "model": model_id,
            "usage": {
                "prompt_tokens": total_prompt_tokens,
                "total_tokens": total_total_tokens,
            },
        }

        return combined_response

    def embed_query(self, text: str) -> List[float]:
        """Compute query embeddings using a Bedrock model.

        Args:
            text: The text to embed.

        Returns:
            Embeddings for the text.
        """
        if self.provider == "cohere":
            response = self._embedding_func(text, input_type="search_query")
        else:
            response = self._embedding_func(text)

        if self.normalize:
            response["embeddings"][0] = self._normalize_vector(
                response["embeddings"][0]
            )

        return response

    async def _aembedding_func(
        self, text: str, input_type: str = "search_document"
    ) -> List[float]:
        """Call out to Bedrock embedding endpoint with a single text."""
        # replace newlines, which can negatively affect performance.
        text = text.replace(os.linesep, " ")

        if self.provider == "cohere":
            # Cohere input_type depends on usage
            # for embedding documents use "search_document"
            # for embedding queries for retrieval use "search_query"
            response_body = await self._ainvoke_model(
                input_body={
                    "input_type": input_type,
                    "texts": [text],
                }
            )
            embedding = response_body.get("embeddings")[0]
            token_count = 0
        else:
            # includes common provider == "amazon"
            response_body = await self._ainvoke_model(
                input_body={"inputText": text},
            )
            embedding = response_body.get("embedding")
            token_count = response_body.get("inputTextTokenCount", 0)

            # Prepare the final response with embedding, model ID, and usage details
            result = {
                "embeddings": [embedding],
                "model": self.model_id,
                "usage": {"prompt_tokens": token_count, "total_tokens": token_count},
            }

            return result

    async def aembed_query(self, text: str) -> List[float]:
        """Asynchronous compute query embeddings using a Bedrock model.

        Args:
            text: The text to embed.

        Returns:
            Embeddings for the text.
        """
        if self.provider == "cohere":
            response = await self._aembedding_func(text, input_type="search_query")
        else:
            response = await self._aembedding_func(text)

        if self.normalize:
            response["embeddings"][0] = await self._anormalize_vector(
                response["embeddings"][0]
            )

        return response

        # return await run_in_executor(None, self.embed_query, text)

    async def _anormalize_vector(self, embeddings: List[float]) -> List[float]:
        """Normalize the embedding to a unit vector."""
        emb = np.array(embeddings)
        norm_emb = emb / np.linalg.norm(emb)
        return norm_emb.tolist()

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """Asynchronous compute doc embeddings using a Bedrock model.

        Args:
            texts: The list of texts to embed

        Returns:
            List of embeddings, one for each text.
        """
        combined_embeddings = []
        total_prompt_tokens = 0
        total_total_tokens = 0
        model_id = self.model_id

        documents_responses = await asyncio.gather(
            *[self.aembed_query(text) for text in texts]
        )

        for document_response in documents_responses:
            combined_embeddings.extend(document_response["embeddings"])
            total_prompt_tokens += document_response["usage"]["prompt_tokens"]
            total_total_tokens += document_response["usage"]["total_tokens"]

        combined_response = {
            "embeddings": combined_embeddings,
            "model": model_id,
            "usage": {
                "prompt_tokens": total_prompt_tokens,
                "total_tokens": total_total_tokens,
            },
        }

        return combined_response

    async def _ainvoke_model(self, input_body: Dict[str, Any] = {}) -> Dict[str, Any]:
        if self.model_kwargs:
            input_body = {**input_body, **self.model_kwargs}

        body = json.dumps(input_body)

        try:
            response = await self.client.invoke_model(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json",
            )
            body_bytes = await response.get("body").read()
            response_body = json.loads(body_bytes)
            return response_body
        except Exception as e:
            logging.exception(f"Error raised by inference endpoint: {e}")
            raise e
