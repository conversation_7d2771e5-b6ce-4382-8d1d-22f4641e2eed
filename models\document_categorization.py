from typing import List
from pydantic import BaseModel, Field, model_validator
import json
from exceptions import ValidationError
from constants import DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED


class DocumentCategoryConfig(BaseModel):
    """Configuration for a document category."""

    documentCategory: str = Field(..., description="The name of the document category")
    categoryPrompt: str = Field(..., description="The prompt question for the category")
    categoryKeywords: List[str] = Field(
        ..., description="List of keywords for the category"
    )


class DocumentCategoriesConfig(BaseModel):
    """Configuration for document categories."""

    categories: List[DocumentCategoryConfig] = Field(
        DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED,
        description="List of document categories",
    )

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        try:
            if isinstance(value, str):
                # Try parsing from JSON string
                parsed = json.loads(value)
                return cls(**parsed)
            elif isinstance(value, dict) and "categories" in value:
                return value
            elif isinstance(value, list):
                return {"categories": value}
            else:
                raise ValueError
        except Exception:
            raise ValidationError(
                "Invalid custom document categories. Please specify valid format or leave empty to use default categories."
            )


class DocumentClassification(BaseModel):
    """Model for document classification results."""

    document_category: str = Field(
        ...,
        description=f"The document category based on content.",
    )
    reasoning: str = Field(
        ...,
        description="Explanation for the classification, referencing patterns and context.",
    )
    confidence_score: float = Field(
        ..., ge=0, le=1, description="Confidence level (0 to 1) of the classification."
    )

    @classmethod
    async def with_categories_description(cls, categories_description: str):
        """Create a DocumentClassification model with a dynamic categories description.

        Args:
            categories_description (str): A formatted string describing the available categories.

        Returns:
            Type[DocumentClassification]: A model class with updated field descriptions.
        """
        # Create a new model class with the same fields but updated description
        updated_model = type("DynamicDocumentClassification", (cls,), {})

        # Update the document_category field description
        updated_model.model_fields = dict(cls.model_fields)
        updated_model.model_fields["document_category"] = Field(
            ...,
            description=f"The document category based on content. Available categories:\n{categories_description}",
        )

        return updated_model
