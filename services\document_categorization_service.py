from fastapi import Request
from typing import List, Dict, Optional, Any, <PERSON><PERSON>
from rapidfuzz import fuzz
import logging
import json
import asyncio
from models.document_categorization import DocumentCategoriesConfig
from services.endpoint_handlers.rag_service import (
    RetrievalAugmentedGeneration,
    SourceDocument,
)
from constants import (
    DOCUMENT_CATEGORIZATION_FUZZY_MATCH_THRESHOLD,
    DOCUMENT_CATEGORIZATION_KEYWORD_MATCH_PERCENTAGE,
    RAG_ENABLED,
    DOCUMENT_CATEGORIZATION_MATCH_WEIGHT,
    DOCUMENT_CATEGORIZATION_LLM_WEIGHT,
    DOCUMENT_CATEGORIZATION_TOTAL_SCORE_THRESHOLD,
    DEFAULT_DOCUMENT_CATEGORIES_CONFIG,
    DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED,
    DEFAULT_LLM_REASONING_ENABLED,
)


async def generate_categories_description(
    categories_config: DocumentCategoriesConfig,
) -> str:
    """
    Generate a formatted description of document categories for use in prompts.
    Args:
        categories_config (DocumentCategoriesConfig): The document categories configuration.
    Returns:
        str: A formatted string describing all categories.
    """
    descriptions = []
    for i, category in enumerate(categories_config.categories, 1):
        if not category.documentCategory or category.documentCategory == "Other":
            continue
        description = f"{i}. **{category.documentCategory}:** {category.categoryPrompt}"
        descriptions.append(description)

    return "\n".join(descriptions)


async def classify_document_into_categories(
    request: Request,
    source_document: SourceDocument,
    prompt: str,
    llm_platform: str,
    embedding_platform: str,
    rag_enabled: str = RAG_ENABLED,
    custom_categories: Optional[DocumentCategoriesConfig] = None,
    llm_reasoning_enabled: str = DEFAULT_LLM_REASONING_ENABLED,
) -> Dict[str, Any]:
    """
    Classify the document into categories using a hybrid approach (RAG + fuzzy matching).
    Args:
        request (Request): The request object.
        source_document: SourceDocument:  The document text or json.
        prompt (str): The prompt for the LLM.
        llm_platform (str): The platform for the LLM.
        embedding_platform (str): The platform for the embedding.
        rag_enabled (str): Whether to use RAG or not.
        custom_categories (DocumentCategoriesConfig, optional): Custom document categories configuration.
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict[str, Any]: A dictionary containing the categorization results using the hybrid approach.
    """
    categories_config = await validate_and_get_categories_config(custom_categories)

    questions = [
        category.categoryPrompt
        for category in categories_config.categories
        if category.categoryPrompt.strip() != ""
    ]

    categories_description = await generate_categories_description(categories_config)

    rag_service = RetrievalAugmentedGeneration(request, llm_reasoning_enabled)
    await rag_service.initialize(
        llm_platform,
        embedding_platform,
        source_document,
        rag_enabled=rag_enabled,
    )

    rag_and_fuzzy_tasks = [
        rag_service.categorize_document(questions, prompt, categories_description),
        categorize_document_using_fuzzy_matching(source_document, categories_config),
    ]

    rag_results, fuzzy_results = await asyncio.gather(*rag_and_fuzzy_tasks)

    combined_results = await combine_rag_and_fuzzy_results(rag_results, fuzzy_results)
    non_existing_categories = await get_non_existing_categories(
        combined_results, categories_config
    )
    combined_results["nonExistingDocumentCategories"] = non_existing_categories
    return combined_results


async def validate_and_get_categories_config(
    custom_categories: DocumentCategoriesConfig,
) -> DocumentCategoriesConfig:
    """
    Validate and get the document categories configuration.
    Args:
        custom_categories (Optional[DocumentCategoriesConfig]): Custom document categories configuration.
    Returns:
        DocumentCategoriesConfig: The validated document categories configuration.
    Note:
        Falls back to default configurations if custom categories are invalid:
        1. First tries to use custom categories
        2. Then tries DEFAULT_DOCUMENT_CATEGORIES_CONFIG
        3. Finally falls back to DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED
    """
    if custom_categories:
        logging.info("Using custom document categories configuration.")
        return DocumentCategoriesConfig(**custom_categories.model_dump())
    try:
        categories_json = (
            json.loads(DEFAULT_DOCUMENT_CATEGORIES_CONFIG)
            if isinstance(DEFAULT_DOCUMENT_CATEGORIES_CONFIG, str)
            else DEFAULT_DOCUMENT_CATEGORIES_CONFIG
        )
        default_env_categories = DocumentCategoriesConfig(categories=categories_json)
        logging.info("Using default document categories from environment.")
        return default_env_categories
    except (json.JSONDecodeError, ValueError) as e:
        logging.error(
            f"Error parsing 'default_env_categories' document categories: {e}"
        )
        logging.info("Falling back to default hardcoded document categories.")
        return DocumentCategoriesConfig(
            categories=DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED
        )


async def combine_rag_and_fuzzy_results(rag_results: Dict, fuzzy_results: Dict) -> Dict:
    """
    Combine RAG and fuzzy matching results.
    Args:
        rag_results (Dict): The RAG-based categorization results.
        fuzzy_results (Dict): The fuzzy matching results.
    Returns:
        Dict: The combined results.
    """
    combined_results = {"existingDocumentCategories": []}

    rag_categories = {}
    if "documentCategories" in rag_results:
        for category_data in rag_results["documentCategories"]:
            category_name = category_data.get("documentCategory")
            if category_name:
                if category_name not in rag_categories:
                    rag_categories[category_name] = {"llmScore": 0, "reasoning": []}

                confidence_score = round(
                    category_data.get("confidenceScore", 0) * 100, 2
                )

                # Update max LLM score
                if confidence_score > rag_categories[category_name]["llmScore"]:
                    rag_categories[category_name]["llmScore"] = confidence_score

                rag_categories[category_name]["reasoning"].append(
                    {
                        "reasoning": category_data.get("reasoning", ""),
                        "confidenceScore": category_data.get("confidenceScore", 0),
                        "pages": category_data.get("pages", []),
                    }
                )

    # Combine RAG and fuzzy results
    for category, fuzzy_data in fuzzy_results.items():
        match_score = fuzzy_data.get("matchScore", 0)
        keyword_match_percentage = fuzzy_data.get("keywordMatchPercentage", 0)
        matched_keywords = fuzzy_data.get("matchedKeywords", [])

        llm_score = 0
        llm_reasoning = []

        if category in rag_categories:
            llm_score = rag_categories[category]["llmScore"]
            llm_reasoning = rag_categories[category]["reasoning"]

        total_score = await calculate_total_score(match_score, llm_score)

        if total_score > DOCUMENT_CATEGORIZATION_TOTAL_SCORE_THRESHOLD:
            combined_results["existingDocumentCategories"].append(
                {
                    "documentCategory": category,
                    "scores": {
                        "totalScore": total_score,
                        "matchScore": match_score,
                        "llmScore": llm_score,
                        "matchWeight": DOCUMENT_CATEGORIZATION_MATCH_WEIGHT,
                        "llmWeight": DOCUMENT_CATEGORIZATION_LLM_WEIGHT,
                    },
                    "reasoning": {
                        "llmReasoning": llm_reasoning,
                        "fuzzyReasoning": {
                            "keywordMatchPercentage": keyword_match_percentage,
                            "matchedKeywords": matched_keywords,
                        },
                    },
                }
            )

    return combined_results


async def calculate_total_score(match_score: float, llm_score: float) -> float:
    """
    Calculate the total score based on match and LLM scores and their weights.
    Args:
        match_score (float): The match score.
        llm_score (float): The LLM score.
    Returns:
        float: The total score.
    """
    total_weight = (
        DOCUMENT_CATEGORIZATION_MATCH_WEIGHT + DOCUMENT_CATEGORIZATION_LLM_WEIGHT
    )
    weighted_score = (
        (match_score * DOCUMENT_CATEGORIZATION_MATCH_WEIGHT)
        + (llm_score * DOCUMENT_CATEGORIZATION_LLM_WEIGHT)
    ) / total_weight
    return round(weighted_score, 2)


async def get_category_keywords(
    categories_config: DocumentCategoriesConfig,
) -> Dict[str, List[str]]:
    """
    Get keywords for each document category from the configuration.
    Args:
        categories_config (DocumentCategoriesConfig): The document categories configuration.
    Returns:
        Dict[str, List[str]]: A dictionary mapping category names to lists of keywords.
    """
    return {
        category.documentCategory: category.categoryKeywords
        for category in categories_config.categories
    }


def _match_keywords_sync(
    keywords: List[str], document_text_lower: str
) -> Tuple[List[Tuple[str, float]], float]:
    keyword_matches = []
    total_score = 0
    words = document_text_lower.split()

    for keyword in keywords:
        keyword_lower = keyword.lower()

        if keyword_lower in document_text_lower:
            keyword_matches.append((keyword, 100))
            total_score += 100
            continue

        keyword_word_count = len(keyword_lower.split())
        if keyword_word_count > 1 and len(words) >= keyword_word_count:
            best_match_score = max(
                fuzz.ratio(keyword_lower, " ".join(words[i : i + keyword_word_count]))
                for i in range(len(words) - keyword_word_count + 1)
            )
            if best_match_score > DOCUMENT_CATEGORIZATION_FUZZY_MATCH_THRESHOLD:
                keyword_matches.append((keyword, best_match_score))
                total_score += best_match_score

    return keyword_matches, total_score


async def process_category_keywords(
    category: str, keywords: List[str], document_text_lower: str
) -> Tuple[str, Dict[str, Any]]:
    """
    Process a single category's keywords against the document text.
    Args:
        category (str): The category name.
        keywords (List[str]): The list of keywords for the category.
        document_text_lower (str): The lowercase document text.
    Returns:
        Tuple[str, Dict[str, Any]]: A tuple of (category, result_dict).
    """
    if category == "Other":
        return category, {
            "matchScore": 0,
            "reasoning": "Other category is not processed with fuzzy matching.",
            "matchedKeywords": [],
            "keywordMatchPercentage": 0,
        }

    logging.info(f"Processing category: '{category}' with fuzzy matching.")

    # Run matching in executor thread pool
    loop = asyncio.get_running_loop()
    keyword_matches, total_score = await loop.run_in_executor(
        None, _match_keywords_sync, keywords, document_text_lower
    )

    min_keywords_to_match = max(
        1, int(len(keywords) * DOCUMENT_CATEGORIZATION_KEYWORD_MATCH_PERCENTAGE)
    )

    if keyword_matches:
        avg_match_score = total_score / len(keyword_matches)
        keyword_match_percentage = (
            len(keyword_matches) / len(keywords) if keywords else 0
        )
        if len(keyword_matches) >= min_keywords_to_match:
            final_score = avg_match_score
        else:
            final_score = avg_match_score * (
                len(keyword_matches) / min_keywords_to_match
            )
        reasoning_parts = [
            f"The document contains {len(keyword_matches)} out of {len(keywords)} keywords typically found in {category} documents: "
        ]
        reasoning_parts.append(
            ", ".join([f"'{match[0]}'" for match in keyword_matches[:3]])
        )
        if len(keyword_matches) > 3:
            reasoning_parts.append(f", and {len(keyword_matches) - 3} more")

        reasoning = "".join(reasoning_parts)

        return category, {
            "matchScore": round(final_score, 2),
            "reasoning": reasoning,
            "matchedKeywords": [match[0] for match in keyword_matches],
            "keywordMatchPercentage": round(keyword_match_percentage * 100, 2),
        }
    else:
        return category, {
            "matchScore": 0,
            "reasoning": f"No keywords related to {category} were found in the document.",
            "matchedKeywords": [],
            "keywordMatchPercentage": 0,
        }


async def categorize_document_using_fuzzy_matching(
    source_document: SourceDocument,
    categories_config: DocumentCategoriesConfig,
) -> Dict[str, Dict[str, Any]]:
    """
    Categorize the document using fuzzy keyword matching.
    Args:
        source_document (SourceDocument): The document text.
        categories_config (DocumentCategoriesConfig): The document categories configuration.
    Returns:
        Dict[str, Dict[str, Any]]: A dictionary mapping category names to match scores and details.
    """
    logging.info("Using fuzzy matching to categorize the document.")
    category_keywords = await get_category_keywords(categories_config)
    document_text_lower = source_document.get(
        "document_text", ""
    ).lower() or " \n ".join(
        [
            doc["content"].lower()
            for doc in source_document.get("document_json").values()
        ]
    )
    # Process all categories concurrently
    tasks = [
        process_category_keywords(category, keywords, document_text_lower)
        for category, keywords in category_keywords.items()
    ]

    category_results = await asyncio.gather(*tasks)
    results = {category: result for category, result in category_results}
    return results


async def get_non_existing_categories(
    combined_results: Dict, categories_config: DocumentCategoriesConfig
) -> List[str]:
    """
    Get categories that don't exist in the document based on the combined results.
    Args:
        combined_results (Dict): The combined results from RAG and fuzzy matching.
        categories_config (DocumentCategoriesConfig): The document categories configuration.
    Returns:
        List[str]: A list of category names that don't exist in the document.
    """
    logging.info("Getting non-existing categories.")
    all_categories = [
        category.documentCategory for category in categories_config.categories
    ]

    existing_categories = set()
    if "existingDocumentCategories" in combined_results:
        existing_categories = {
            category["documentCategory"]
            for category in combined_results["existingDocumentCategories"]
        }

    return [
        category
        for category in all_categories
        if category not in existing_categories and category != "Other"
    ]
