from db.base import Base
from sqlalchemy import Column, Integer, String, DateTime, Float
from datetime import datetime, timezone
from fastapi import Request
from typing import Optional


class EndpointSummary(Base):
    """
    The endpoint summary model.
    """

    __tablename__ = "endpoint_summary"

    id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    endpoint = Column(String, nullable=False)
    word_count = Column(Integer)
    total_tokens = Column(Integer)
    prompt_tokens = Column(Integer)
    completion_tokens = Column(Integer)
    total_embedding_tokens = Column(Integer)
    total_cost = Column(Float)
    received_at = Column(DateTime(timezone=True))
    instance_type = Column(String)
    platform = Column(String)
    embedding_platform = Column(String)
    llm_model = Column(String)
    embedding_model = Column(String)
    client_name = Column(String)

    def set_summary(
        self,
        word_count: int,
        total_tokens: int,
        prompt_tokens: int,
        completion_tokens: int,
        total_cost: float,
        total_embedding_tokens: int,
    ) -> None:
        """
        Set the summary for the endpoint.
        Args:
            word_count (int): The word count.
            total_tokens (int): The total tokens.
            prompt_tokens (int): The prompt tokens.
            completion_tokens (int): The completion tokens.
            total_cost (float): The total cost.
            total_embedding_tokens (int): The total embedding tokens.
        """
        self.word_count = word_count
        self.total_tokens = total_tokens
        self.prompt_tokens = prompt_tokens
        self.completion_tokens = completion_tokens
        self.total_cost = total_cost
        self.total_embedding_tokens = total_embedding_tokens

    def set_llm_platform(self, platform: str) -> None:
        """
        Set the LLM platform.
        Args:
            platform (str): The LLM platform.
        """
        self.platform = platform

    def set_llm_model(self, llm_model: str) -> None:
        """
        Set the LLM model.
        Args:
            llm_model (str): The LLM model.
        """
        self.llm_model = llm_model

    def set_embedding_platform(self, embedding_platform: str) -> None:
        """
        Set the embedding platform.
        Args:
            embedding_platform (str): The embedding platform.
        """
        self.embedding_platform = embedding_platform

    def set_embedding_model(self, embedding_model: str) -> None:
        """
        Set the embedding model.
        Args:
            embedding_model (str): The embedding model.
        """
        self.embedding_model = embedding_model

    def set_received_at(self) -> None:
        """
        Set the received at time.
        """
        self.received_at = str(datetime.now(timezone.utc))


async def update_endpoint_summary(
    request: Request,
    total_word_count: Optional[int] = 0,
    total_tokens: Optional[int] = 0,
    prompt_tokens: Optional[int] = 0,
    completion_tokens: Optional[int] = 0,
    total_cost: Optional[float] = 0,
    total_embedding_tokens: Optional[int] = 0,
) -> None:
    """
    Update the endpoint summary.
    Args:
        request (Request): The request object.
        total_word_count (int, optional): The total word count. Defaults to 0.
        total_tokens (int, optional): The total tokens. Defaults to 0.
        prompt_tokens (int, optional): The prompt tokens. Defaults to 0.
        completion_tokens (int, optional): The completion tokens. Defaults to 0.
        total_cost (float, optional): The total cost. Defaults to 0.
        total_embedding_tokens (int, optional): The total embedding tokens. Defaults to 0.
    """
    request.state.endpoint_summary.set_summary(
        total_word_count,
        total_tokens,
        prompt_tokens,
        completion_tokens,
        total_cost,
        total_embedding_tokens,
    )
