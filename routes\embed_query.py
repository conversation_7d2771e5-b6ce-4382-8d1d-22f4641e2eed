from fastapi import (
    Depends,
    APIRouter,
    Request,
    Form,
)
from constants import (
    DISABLE_BASIC_AUTH,
    EMBEDDING_PLATFORM_EMBED_QUERY,
)
from auth import authenticate_request
from utils.enums import CloudPlatform, EndpointDescriptions, Questions
from services.ocr_and_embeddings import get_query_embeddings
from utils.cloud_utils import get_endpoint_embedding_platform
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)
from typing import Dict, Any

# Get the endpoint model configurations
EMBEDDING_PLATFORM = get_endpoint_embedding_platform(
    EMBEDDING_PLATFORM_EMBED_QUERY, "/embed-query"
)

embed_query_router = APIRouter()


@embed_query_router.post(
    "/embed-query",
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.embed_query,
)
async def embed_query(
    request: Request,
    questions: Questions = Form(
        None,
        description="List of questions that needs to be embedded.",
    ),
    embedding_platform: CloudPlatform = Form(
        EMBEDDING_PLATFORM,
        description=f"Optional: Embedding Platform to use, could be 'azure' or 'aws'. Default is {EMBEDDING_PLATFORM}.",
    ),
) -> Dict[str, Any]:
    """
    Route to get the embeddings against the user questions.
    Args:
        request (Request): The request object.
        questions (Questions, optional): The list of questions for the AI to answer.
        embedding_platform (Platform, optional): The embedding platform to use.
    Returns:
        Dict[str, Any]: The embeddings against the questions.
    """
    response = await get_query_embeddings(
        request, embedding_platform, questions.questions
    )
    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response
    )
    return response
