from typing import Any, List, Optional
from langchain_core.callbacks import CallbackManager<PERSON><PERSON><PERSON><PERSON>un
import asyncio
from langchain_aws import ChatBedrockConverse
from langchain_aws.chat_models.bedrock_converse import (
    _messages_to_bedrock,
    _snake_to_camel_keys,
    _parse_response,
)
from langchain_core.outputs import ChatGeneration, ChatResult
from langchain_core.messages import BaseMessage
import logging


class CustomChatBedrockConverse(ChatBedrockConverse):

    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Top Level call"""
        bedrock_messages, system = _messages_to_bedrock(messages)
        params = self._converse_params(
            stop=stop,
            **_snake_to_camel_keys(
                kwargs, excluded_keys={"inputSchema", "properties", "thinking"}
            ),
        )
        logging.info("Using Custom Bedrock Converse API to generate response")
        response = await self.client.converse(
            messages=bedrock_messages, system=system, **params
        )
        # logging.info(f"Response from Bedrock: {response}")
        response_message = _parse_response(response)
        response_message.response_metadata["model_name"] = self.model_id
        return ChatResult(generations=[ChatGeneration(message=response_message)])
