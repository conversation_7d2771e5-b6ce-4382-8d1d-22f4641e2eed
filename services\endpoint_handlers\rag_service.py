import logging
import asyncio
from typing import Union, Optional, List, Tuple, Dict, Any
from fastapi import Request, status
from utils import convert_text_to_dict
from utils.custom_text_splitter import RecursiveWordTextSplitter
from utils.cloud_utils import (
    get_cloud_object,
    get_embedding_deployment_name,
    get_llm_deployment_name,
    get_cloud_name,
)
from utils.document_utils import calculate_pdf_word_count
from constants import (
    EMBEDDING_WORKERS,
    RAG_ENABLED,
    RAG_QUERY_PROMPT,
    RAG_JSON_PROMPT,
    QA_SYSTEM_PROMPT,
    RAG_USER_QUESTION,
    QUERY_EXPANSION_SYSTEM_PROMPT,
    EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT,
    EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT,
    PARENT_CHUNK_WORD_COUNT,
    PARENT_CHUNK_OVERLAPPING_WORD_COUNT,
    NUMBER_OF_RETRIEVED_PARENT_CHUNKS,
    ENTIRE_PAGE_AS_PARENT_CHUNK_ENABLED,
    PARENT_CHUNK_ENABLED,
    NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT,
    NUMBER_OF_CHUNKS_FOR_LLM,
    DISABLE_DOCUMENT_RERANKING,
    RAG_WORKERS,
    RERANKING_AUTH_KEY,
    RERANKING_API_URL,
    DISABLE_DOCUMENT_RERANKING_AUTH,
    RERANKING_SERVICE_TIMEOUT,
    EXTRACT_FIELDS_SYSTEM_PROMPT,
    DATA_POINT_UNIT_SEPARATOR,
    DOCUMENT_CATEGORIZATION_SYSTEM_PROMPT,
    VECTOR_STORE_WORKERS,
    DEFAULT_LLM_REASONING_ENABLED,
)
from models import update_endpoint_summary, DocumentClassification
from langchain_community.vectorstores import FAISS
from langchain.docstore.document import Document
import re
from exceptions import CustomError
from langchain_community.vectorstores.utils import DistanceStrategy
import faiss
from langchain_community.docstore.in_memory import InMemoryDocstore
from pydantic import BaseModel, Field
from utils.enums import ExtractionType
import json
import httpx
from utils.document_utils import SourceDocument
from utils.unit_conversion import convert_unit_of_measurement
from utils import post_process_data_points


class ParaphrasedQuery(BaseModel):
    """You have performed query expansion to generate a paraphrasing of a question."""

    paraphrased_query: str = Field(
        ...,
        description="The original question.",
    )


class FaissVectorStore:
    """
    FaissVectorStore class for creating and managing a FAISS vector store.
    """

    def __init__(
        self,
    ):
        """
        Initializes the FaissVectorStore class.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        """
        self.parent_chunks = None
        self.vector_store = None

    async def initialize(self, request: Request, embedding_platform: str):
        self.cloud_object = await get_cloud_object(embedding_platform)
        request.state.endpoint_summary.set_embedding_platform(
            await get_cloud_name(embedding_platform)
        )
        request.state.endpoint_summary.set_embedding_model(
            await get_embedding_deployment_name(embedding_platform)
        )

    async def create_chunks(
        self, source_document: SourceDocument, html: bool = False
    ) -> List[Document]:
        """
        Creates chunks from the document text.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            List[Document]: A list of documents.
        """
        chunks = await self.convert_page_text_to_chunks(source_document, html)
        text_splitter = RecursiveWordTextSplitter(
            chunk_size=EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT,
            chunk_overlap=EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT,
        )
        if PARENT_CHUNK_ENABLED == "true":
            self.parent_chunks = await self.create_parent_chunks(chunks, html)
            texts = await text_splitter.split_documents(
                self.parent_chunks, "child_chunk_num"
            )
        else:
            texts = await text_splitter.split_documents(chunks, "child_chunk_num")
        logging.info(f"{len(texts)} document chunks created.")
        return texts

    async def convert_page_text_to_chunks(
        self, source_document: SourceDocument, html: bool = False
    ) -> List[Document]:
        """
        Converts the document text to chunk format.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            List[Document]: A list of documents.
        """
        chunks = []
        if not html and source_document.get("document_json"):
            logging.info(
                f"Creating embeddings for {len(source_document.get('document_json'))} pages."
            )
            for idx, page_no in enumerate(source_document.get("document_json")):
                chunks.append(
                    Document(
                        page_content=source_document.get("document_json")[page_no].get(
                            "content", ""
                        ),
                        metadata={
                            "parent_chunk_num": str(idx + 1),
                            "page": str(page_no),
                        },
                    )
                )
        else:
            chunks.append(
                Document(
                    page_content=source_document.get("document_text"),
                    metadata={"parent_chunk_num": "1", "page": "HTML"},
                )
            )
        return chunks

    async def create_parent_chunks(
        self, chunks: List[Document], html: bool = False
    ) -> List[Document]:
        """
        Create parent chunks.
        Args:
            chunks (List[Document]): The converted chunks from document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            List[Document]: A list of documents.
        """
        if not html and ENTIRE_PAGE_AS_PARENT_CHUNK_ENABLED == "true":
            return chunks
        parent_splitter = RecursiveWordTextSplitter(
            chunk_size=PARENT_CHUNK_WORD_COUNT,
            chunk_overlap=PARENT_CHUNK_OVERLAPPING_WORD_COUNT,
        )
        parent_chunks = await parent_splitter.split_documents(
            chunks, "parent_chunk_num"
        )
        return parent_chunks

    async def get_top_k_parent_chunks(
        self, child_chunks: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Returns parent chunks from top K child chunks and removes all child chunks of mapped parents.
        Args:
            child_chunks (List[Tuple[Document,float]]): The list of child chunks.
        Returns:
            List[Tuple[Document,float]]: The list of parent chunks.
        """
        parent_chunks_dict = {
            chunk.metadata["parent_chunk_num"]: chunk for chunk in self.parent_chunks
        }

        retrieved_parent_chunk_nums = set()
        combined_parent_child_chunks = []

        for i, child_chunk in enumerate(child_chunks):
            parent_chunk_num = child_chunk[0].metadata["parent_chunk_num"]
            if (
                i < NUMBER_OF_RETRIEVED_PARENT_CHUNKS
                and parent_chunk_num in parent_chunks_dict
                and parent_chunk_num not in retrieved_parent_chunk_nums
            ):
                combined_parent_child_chunks.append(
                    (parent_chunks_dict[parent_chunk_num], None)
                )
                retrieved_parent_chunk_nums.add(parent_chunk_num)
            elif parent_chunk_num not in retrieved_parent_chunk_nums:
                combined_parent_child_chunks.append(child_chunk)

        return combined_parent_child_chunks

    async def create_document_embeddings(
        self, source_document: SourceDocument, html: bool = False
    ) -> Dict[str, Any]:
        """
        Creates document embeddings.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            Dict[str, Any]: A dict containing the embeddings of the document and the token usage.
        """
        data_with_embeddings = []
        texts = await self.create_chunks(source_document, html)
        embedded_docs = await self.cloud_object.embed_documents(
            [text.page_content for text in texts]
        )
        for idx, chunk_embeddings in enumerate(embedded_docs["embeddings"]):
            data_with_embeddings.append(
                {
                    "metadata": texts[idx].metadata,
                    "text": texts[idx].page_content,
                    "embedding": chunk_embeddings,
                }
            )
        return {"usage": embedded_docs["usage"], "embedding_data": data_with_embeddings}

    async def initialize_vector_store_and_return_tokens(
        self, source_document: SourceDocument, html: bool = False
    ) -> Tuple[FAISS, int]:
        """
        Initialize a FAISS vector store.
        Args:
            source_document: (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            Tuple[FAISS, int]: A tuple containing the FAISS vector store and the total number of embedding tokens.
        """
        embeddings_data = await self.create_document_embeddings(source_document, html)
        total_embedding_tokens = embeddings_data["usage"]["total_tokens"]
        self.vector_store = FAISS(
            embedding_function=None,
            index=faiss.IndexFlatL2(
                len(embeddings_data["embedding_data"][0]["embedding"])
            ),
            distance_strategy=DistanceStrategy.EUCLIDEAN_DISTANCE,
            docstore=InMemoryDocstore(),
            index_to_docstore_id={},
        )
        _ = self.vector_store.add_embeddings(
            text_embeddings=zip(
                [
                    chunk_data["text"]
                    for chunk_data in embeddings_data["embedding_data"]
                ],
                [
                    chunk_data["embedding"]
                    for chunk_data in embeddings_data["embedding_data"]
                ],
            ),
            metadatas=[
                chunk_data["metadata"]
                for chunk_data in embeddings_data["embedding_data"]
            ],
        )

        return total_embedding_tokens

    async def similarity_search(
        self, query_embedding: List[float], k: int = 2
    ) -> List[Tuple[Document, float]]:
        """
        Performs a similarity search and returns the relevant chunks.
        Args:
            query_embedding (List[float]): The query embedding.
            k (int, optional): The number of results to return. Default is 2.
        Returns:
            List[Tuple[Document, float]]: A list of tuples containing the documents and their scores.
        """
        if not self.vector_store:
            raise ValueError("Vector store has not been created yet.")
        return await self.vector_store.asimilarity_search_with_score_by_vector(
            query_embedding, k=k
        )


class RetrievalAugmentedGeneration:
    """
    RetrievalAugmentedGeneration class for performing retrieval-augmented generation.
    """

    def __init__(
        self,
        request: Request,
        llm_reasoning_enabled: str = DEFAULT_LLM_REASONING_ENABLED,
    ):
        """
        Initializes the RetrievalAugmentedGeneration class.
        Args:
            request (Request): The request object.
            llm_reasoning_enabled (str, optional): Whether to enable LLM reasoning. Default is LLM_REASONING_ENABLED.
        """
        self.request = request
        self.llm_reasoning_enabled = llm_reasoning_enabled

    async def initialize(
        self,
        llm_platform: str,
        embedding_platform: str,
        source_document: SourceDocument,
        html: bool = False,
        rag_enabled: str = RAG_ENABLED,
    ):
        self.llm_object = await get_cloud_object(llm_platform)
        self.llm_deployment_name = await get_llm_deployment_name(
            llm_platform, self.llm_reasoning_enabled
        )
        pdf_word_count = await calculate_pdf_word_count(source_document)
        await self.request.state.token_tracker.set_word_count(pdf_word_count)
        self.request.state.endpoint_summary.set_llm_platform(self.llm_object.cloud_name)

        if rag_enabled == "true":
            self.faiss_obj = FaissVectorStore()
            await self.faiss_obj.initialize(self.request, embedding_platform)
            total_embedding_tokens = (
                await self.faiss_obj.initialize_vector_store_and_return_tokens(
                    source_document, html
                )
            )
            await self.request.state.token_tracker.update(
                {"total_embedding_tokens": total_embedding_tokens}
            )
        else:
            self.document_text = source_document.get("document_text") or "\n".join(
                [
                    doc.get("content", "")
                    for doc in source_document.get("document_json").values()
                ]
            )

        await self._update_endpoint_summary()

    async def get_context_for_llm(self, question: str) -> Tuple[str, Dict[str, Any]]:
        """
        Gets the context for the LLM.
        Args:
            question (str): The question.
        Returns:
            Tuple[str, Dict[str, Any]]: A tuple containing the context for the LLM and the relevant chunk pages.
        """
        variants = await self.query_expansion(question)
        variants.append(question)

        tasks = [self._get_relevant_docs_for_query(variant) for variant in variants]
        all_chunks = await asyncio.gather(*tasks)

        relevant_chunks = await self._remove_chunk_duplication(all_chunks)

        if DISABLE_DOCUMENT_RERANKING == "false":
            logging.info(f"Reranking chunks for '{question}'")
            relevant_chunks = await self.document_reranking(question, relevant_chunks)
        else:
            relevant_chunks = await self._sort_chunks_by_score_and_pick_top_k(
                relevant_chunks
            )

        if PARENT_CHUNK_ENABLED == "true":
            relevant_chunks = await self.faiss_obj.get_top_k_parent_chunks(
                relevant_chunks
            )

        all_context, all_pages = await self._prepare_chunks_for_llm(relevant_chunks)
        return all_context, all_pages

    async def _process_question(
        self,
        question: str,
        qa_prompt: str,
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Process a single question and get the answer with context.
        Args:
            question (str): The question to process.
            qa_prompt (str): The QA prompt.
        Returns:
            Tuple[str, Dict[str, Any]]: A tuple containing the question and its answer with context.
        """
        all_context, all_pages = await self.get_context_for_llm(question)
        question_prompt = (
            qa_prompt + "\n\n" + RAG_USER_QUESTION.format(question=question)
        )
        question_prompt = RAG_QUERY_PROMPT.format(
            context=all_context, question=question_prompt
        )
        response = await self.llm_object.get_llm_response_with_metadata(
            question_prompt,
            "json_object",
            QA_SYSTEM_PROMPT,
            self.llm_reasoning_enabled,
        )
        self.request.state.endpoint_summary.set_llm_model(self.llm_deployment_name)
        await self._update_token_track(response)
        await self._update_endpoint_summary()

        llm_response = response.get("content", "")
        try:
            answer_json = await convert_text_to_dict(llm_response)
        except Exception as e:
            logging.exception(
                f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
            )
            answer_json = await self._extract_json_with_regex(llm_response)

        answer_json["response"] = ", ".join(answer_json.values())
        answer_json["pages"] = all_pages
        return question, answer_json

    async def get_answer_with_context(
        self,
        questions: List[str],
        qa_prompt: str,
        default_data_points: Optional[List[str]] = ["Height||in", "Weight||lbs"],
        rag_enabled: str = RAG_ENABLED,
    ) -> Dict[str, Any]:
        """
        Gets the answer with context.
        Args:
            questions (List[str]): The questions.
            qa_prompt (str): The QA prompt.
            default_data_points (str, optional): The default data points. Default is "Height (inches), Weight (pounds)".
        Returns:
            Dict[str, Any]: A dictionary containing the answers with context.
        """
        answers_with_context = {}
        valid_questions = [q.strip() for q in questions if q.strip()]

        if valid_questions:
            tasks = [
                self._process_question(question, qa_prompt)
                for question in valid_questions
            ]
            results = await asyncio.gather(*tasks)
            for question, answer in results:
                answers_with_context[question] = answer

        extract_data_points = await self.extract_data_points(
            default_data_points, rag_enabled=rag_enabled
        )
        extract_data_points = await post_process_data_points(
            extract_data_points, default_data_points
        )
        answers_with_context["default"] = extract_data_points
        return answers_with_context

    async def _process_category_question(
        self,
        question: str,
        prompt: str,
        dynamic_doc_classification: Any,
        categories_description: str,
    ) -> List[Dict[str, Any]]:
        """
        Process a single category question.
        Args:
            question (str): The question to process.
            prompt (str): The prompt for document categorization.
            dynamic_doc_classification (Any): The document classification model.
            categories_description (str): The description of the categories.
        Returns:
            List[Dict[str, Any]]: A list of document category results.
        """
        try:
            all_context, all_pages = await self.get_context_for_llm(question)
            question_prompt = RAG_QUERY_PROMPT.format(
                context=all_context, question=prompt
            )

            llm_response_and_tokens_usage = (
                await self.llm_object.get_llm_pydantic_schema_response_with_metadata(
                    question_prompt,
                    dynamic_doc_classification,
                    DOCUMENT_CATEGORIZATION_SYSTEM_PROMPT.format(
                        DOCUMENT_CATEGORIES=categories_description
                    ),
                    self.llm_reasoning_enabled,
                )
            )

            # Update token tracking and endpoint summary
            self.request.state.endpoint_summary.set_llm_model(self.llm_deployment_name)
            await self._update_token_track(
                llm_response_and_tokens_usage["tokens_usage"]
            )
            await self._update_endpoint_summary()

            llm_response: List[DocumentClassification] = llm_response_and_tokens_usage[
                "llm_response"
            ]
            logging.info(
                f"Document Classification for question '{question}': {llm_response}"
            )

            if llm_response:
                return [
                    {
                        "documentCategory": doc_class.document_category,
                        "reasoning": doc_class.reasoning,
                        "confidenceScore": doc_class.confidence_score,
                        "pages": [int(page) for page in all_pages.keys()],
                    }
                    for doc_class in llm_response
                ]
        except Exception as e:
            logging.error(
                f"Exception while processing category question - {question}: {e}"
            )
        return []

    async def categorize_document(
        self, questions: List[str], prompt: str, categories_description: str
    ) -> Dict[str, Any]:
        """
        Categorize the document into categories based on the content.
        Args:
            questions (List[str]): The questions to categorize.
            prompt (str): The prompt for document categorization.
            categories_description (str): The description of the categories.
        Returns:
            Dict[str, Any]: A dictionary containing the categorization results.
        """
        logging.info("Using AI RAG to categorize the document.")
        answers_with_context = {"documentCategories": []}

        dynamic_doc_classification = (
            await DocumentClassification.with_categories_description(
                categories_description
            )
        )

        tasks = [
            self._process_category_question(
                question, prompt, dynamic_doc_classification, categories_description
            )
            for question in questions
        ]
        results = await asyncio.gather(*tasks)
        for result in results:
            answers_with_context["documentCategories"].extend(result)

        return answers_with_context

    async def _sort_chunks_by_score_and_pick_top_k(
        self, chunks: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Sorts the chunks by score and picks the top k chunks.
        Args:
            chunks (List[Tuple[Document, float]]): The chunks to sort.
        Returns:
            List[Tuple[Document, float]]: The sorted list of the top k chunks.
        """
        chunks.sort(key=lambda x: x[1], reverse=False)
        return chunks[:NUMBER_OF_CHUNKS_FOR_LLM]

    async def _remove_chunk_duplication(
        self, chunks_list: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Removes duplicate chunks based on chunk number in metadata.
        Args:
            chunks (List[Tuple[Document, float]]): The chunks to remove duplicates from.
        Returns:
            List[Tuple[Document, float]]: A list of unique chunks.
        """
        unique_chunks = {}
        for chunks in chunks_list:
            for chunk in chunks:
                chunk_num = chunk[0].metadata.get("child_chunk_num")
                if chunk_num not in unique_chunks:
                    chunk = (chunk[0], float(chunk[1]))
                    unique_chunks[chunk_num] = chunk
        return list(unique_chunks.values())

    async def query_expansion(self, question: str) -> List[str]:
        """
        Expands a given question into multiple paraphrased variants using an LLM.
        Args:
            question (str): The original question to be expanded.
        Returns:
            List[str]: A list of paraphrased versions of the original question.
        """
        llm_response_and_tokens_usage = (
            await self.llm_object.get_llm_pydantic_schema_response_with_metadata(
                question,
                ParaphrasedQuery,
                QUERY_EXPANSION_SYSTEM_PROMPT,
                self.llm_reasoning_enabled,
            )
        )

        await self._update_token_track(llm_response_and_tokens_usage["tokens_usage"])
        await self._update_endpoint_summary()
        llm_paraphrased_queries: List[ParaphrasedQuery] = llm_response_and_tokens_usage[
            "llm_response"
        ]
        logging.info(f"Query variants for '{question}': {llm_paraphrased_queries}")
        return [query.paraphrased_query for query in llm_paraphrased_queries]

    async def document_reranking(
        self, query: str, relevant_chunks: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Reranks the chunks based on the query.
        Args:
            query (str): The query.
            relevant_chunks (List[Tuple[Document, float]]): The chunks to rerank.
        Returns:
            List[Tuple[Document, float]]: A list of reranked chunks.
        """
        # Prepare chunks for reranking format
        chunks_json = [
            {
                "id": chunk[0].metadata.get("child_chunk_num"),
                "text": chunk[0].page_content,
                "metadata": chunk[0].metadata,
            }
            for chunk in relevant_chunks
        ]

        params = {"query": query, "K": NUMBER_OF_CHUNKS_FOR_LLM}
        files = {
            "chunks_file": (
                "chunks_file.json",
                json.dumps(chunks_json),
                "application/json",
            )
        }
        headers = None
        if DISABLE_DOCUMENT_RERANKING_AUTH != "true":
            headers = {"Authorization": f"Basic {RERANKING_AUTH_KEY}"}
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    RERANKING_API_URL,
                    params=params,
                    files=files,
                    headers=headers,
                    timeout=RERANKING_SERVICE_TIMEOUT,
                )
                response.raise_for_status()
                reranked_chunks = [
                    (
                        Document(
                            page_content=chunk["text"], metadata=chunk["metadata"]
                        ),
                        chunk["score"],
                    )
                    for chunk in response.json()["rerankedChunks"]
                ]
                return reranked_chunks
            except httpx.HTTPStatusError as e:
                logging.exception(f"Error occured while reranking: {e.response.text}")
                raise CustomError(
                    f"Error occured while reranking: {e.response.text}",
                    e.response.status_code,
                )
            except Exception as e:
                logging.exception(f"An error occurred while reranking: {e}")
                raise CustomError(
                    f"An error occurred while reranking: {e}",
                    status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

    async def extract_data_points(
        self,
        data_points: List[str],
        extraction_method: str = "af",
        rag_enabled=RAG_ENABLED,
    ) -> Dict[str, Any]:
        """
        Extracts data points.
        Args:
            data_points (List[str]): The data points.
            extraction_method (str, optional): The extraction method. Default is "af".
            rag_enabled (str, optional): Whether to use RAG or skip it.
        Returns:
            Dict[str, Any]: A dictionary containing the extracted data points.
        """
        if extraction_method == ExtractionType.AllField:
            return await self._extract_all_fields(data_points, rag_enabled)
        else:
            return await self._extract_separate_fields(data_points)

    async def _extract_relevant_chunks(
        self, data_point: str
    ) -> Tuple[str, List, Dict[str, Any]]:
        """
        Extracts relevant chunks for a given data point.
        Args:
            data_point (str): The data point.
        Returns:
            Tuple[str, List, Dict[str, Any]]: A tuple containing the data point, context for the LLM, and relevant chunk pages.
        """
        data_point = data_point.strip()
        relevant_chunks = await self._get_relevant_docs_for_query(
            data_point, k=NUMBER_OF_CHUNKS_FOR_LLM
        )
        context_for_llm, relevant_chunk_pages = await self._get_prepared_chunks_for_llm(
            relevant_chunks
        )
        return data_point, context_for_llm, relevant_chunk_pages

    async def _extract_all_fields(
        self, data_points: List[str], rag_enabled=RAG_ENABLED
    ) -> Dict[str, Any]:
        """
        Extracts all fields.
        Args:
            data_points (List[str]): The data points.
            rag_enabled (str, optional): Whether to use RAG or skip it.
        Returns:
            Dict[str, Any]: A dictionary containing the extracted data points.
        """
        context_json = {}
        relevant_docs = set()

        json_to_fill = {
            data_point.split(DATA_POINT_UNIT_SEPARATOR)[0].strip(): ""
            for data_point in data_points
        }
        conversion_map = {
            data_point.split(DATA_POINT_UNIT_SEPARATOR)[0].strip(): {
                "data_point": data_point,
                "desired_unit": data_point.split(DATA_POINT_UNIT_SEPARATOR)[1]
                .lower()
                .strip(),
            }
            for data_point in data_points
            if DATA_POINT_UNIT_SEPARATOR in data_point
        }

        if rag_enabled == "true":
            semaphore = asyncio.Semaphore(RAG_WORKERS)
            results = []

            async def call_with_limit(data_point):
                async with semaphore:
                    return await self._extract_relevant_chunks(data_point)

            tasks = [call_with_limit(data_point) for data_point in json_to_fill]
            results = await asyncio.gather(*tasks)
            for data_point, context_for_llm, relevant_chunk_pages in results:
                relevant_docs.update(context_for_llm)
                context_json[data_point] = relevant_chunk_pages

        else:
            relevant_docs = [self.document_text]

        text_response = await self._extract_data_points_using_llm(
            json_to_fill, " ".join(relevant_docs)
        )
        try:
            json_response = await convert_text_to_dict(text_response)
            if conversion_map:
                json_response = await convert_unit_of_measurement(
                    json_response, conversion_map
                )
            json_response["Chunks"] = context_json
            return json_response
        except Exception as e:
            logging.exception(
                f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
            )
            return await self._extract_json_with_regex(text_response, context_json)

    async def _extract_separate_fields(self, data_points: List[str]) -> Dict[str, Any]:
        """
        Extracts separate fields.
        Args:
            data_points (List[str]): The data points.
        Returns:
            Dict[str, Any]: A dictionary containing the extracted data points.
        """
        context_json = {}
        final_response = {}
        for data_point in data_points:
            data_point = data_point.strip()
            if data_point:
                json_to_fill = {data_point: ""}
                relevant_chunks = await self._get_relevant_docs_for_query(
                    data_point, k=NUMBER_OF_CHUNKS_FOR_LLM
                )
                (context_for_llm, relevant_chunk_pages) = (
                    await self._prepare_chunks_for_llm(relevant_chunks)
                )
                context_json[data_point] = relevant_chunk_pages
                response = await self._extract_data_points_using_llm(
                    json_to_fill, context_for_llm
                )
                try:
                    response = await convert_text_to_dict(response)
                    final_response.update(response)
                except:
                    response = await self._extract_json_with_regex(response)
                    if isinstance(response, dict):
                        final_response.update(response)
                    else:
                        final_response.update({data_point: response})
        final_response["Chunks"] = context_json
        return final_response

    async def _get_prepared_chunks_for_llm(
        self, relevant_chunks: List[Tuple[Document, float]]
    ) -> Tuple[List, Dict[str, Any]]:
        """
        Prepares the chunks for the LLM.
        Args:
            relevant_chunks (List[Tuple[Document, float]]): The relevant chunks.
        Returns:
            Tuple[List, Dict[str, Any]]: A tuple containing the context for the LLM and the relevant chunk pages.
        """
        sorted_chunks = sorted(
            relevant_chunks, key=lambda chunk: chunk[0].metadata["child_chunk_num"]
        )
        context_for_llm = [doc.page_content for doc, _ in sorted_chunks]
        relevant_chunk_pages = {
            page: "\n\n".join(
                doc.page_content
                for doc, _ in sorted_chunks
                if doc.metadata["page"] == page
            )
            for page in {doc.metadata["page"] for doc, _ in sorted_chunks}
        }

        return context_for_llm, relevant_chunk_pages

    async def _prepare_chunks_for_llm(
        self,
        relevant_chunks: List[Tuple[Document, float]],
        qa_call: bool = False,
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Prepares the chunks for the LLM.
        Args:
            relevant_chunks (List[Tuple[Document, float]]): The relevant chunks.
            qa_call (bool, optional): Whether the chunks are for a QA call. Default is False.
        Returns:
            Tuple[str, Dict[str, Any], set]: A tuple containing the context for the LLM, the relevant chunk pages, and the chunk numbers.
        """
        context_for_llm = ""
        relevant_chunk_pages = {}

        for doc, score in relevant_chunks:
            page_content = doc.page_content
            page = doc.metadata["page"]
            if page not in relevant_chunk_pages:
                relevant_chunk_pages[page] = page_content
            else:
                relevant_chunk_pages[page] = (
                    relevant_chunk_pages[page] + " \n\n " + page_content
                )
            context_for_llm += page_content + " "

        return context_for_llm, relevant_chunk_pages

    async def _get_relevant_docs_for_query(
        self, query: str, k: int = NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT
    ) -> List[Tuple[Document, float]]:
        """
        Gets the relevant chunks for the query.
        Args:
            query (str): The query.
            k (int, optional): The number of chunks to retrieve. Default is NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT.
        Returns:
            List[Tuple[Document, float]]: A list of tuples containing the relevant chunks and their scores.
        """
        logging.info(f"Embedding the User Query: {query}")
        query_embedding, usage, _ = await self.llm_object.embed_query(query=query)
        await self.request.state.token_tracker.update(
            {"total_embedding_tokens": usage["total_tokens"]}
        )
        await self._update_endpoint_summary()
        logging.info(f"Fetching Chunks for: '{query}'")
        relevant_chunks = await self.faiss_obj.similarity_search(query_embedding, k=k)
        return relevant_chunks

    async def _extract_data_points_using_llm(
        self, json_to_fill: Dict[str, Any], relevant_docs: str
    ) -> str:
        """
        Extracts data points using the LLM.
        Args:
            json_to_fill (Dict[str, Any]): The JSON to fill.
            relevant_docs (str): The relevant documents.
        Returns:
            str: The extracted data points.
        """
        query_prompt = RAG_QUERY_PROMPT.format(
            context=relevant_docs,
            question=RAG_JSON_PROMPT.format(json_to_fill=json_to_fill),
        )
        response = await self.llm_object.get_llm_response_with_metadata(
            query_prompt,
            "json_object",
            EXTRACT_FIELDS_SYSTEM_PROMPT,
            self.llm_reasoning_enabled,
        )
        self.request.state.endpoint_summary.set_llm_model(self.llm_deployment_name)
        await self._update_token_track(response)
        await self._update_endpoint_summary()
        return response.get("content", "")

    async def _update_token_track(self, response: Dict[str, Any]) -> None:
        """
        Updates the token usage.
        Args:
            response (Dict[str, Any]): The response from the LLM that contains the token usage.
        """
        await self.request.state.token_tracker.update(
            {
                "total_tokens": response.get("total_tokens", 0),
                "prompt_tokens": response.get("prompt_tokens", 0),
                "completion_tokens": response.get("completion_tokens", 0),
                "total_cost": response.get("total_cost", 0),
            }
        )

    async def _update_endpoint_summary(self) -> None:
        """
        Updates the endpoint summary.
        """
        usage = await self.request.state.token_tracker.get_usage()
        await update_endpoint_summary(
            self.request,
            total_word_count=usage["total_word_count"],
            total_tokens=usage["total_tokens"],
            prompt_tokens=usage["prompt_tokens"],
            completion_tokens=usage["completion_tokens"],
            total_embedding_tokens=usage["total_embedding_tokens"],
            total_cost=usage["total_cost"],
        )

    async def _extract_json_with_regex(
        self, response: str, context_json: Optional[Dict[str, Any]] = None
    ) -> Union[Dict[str, Any], str]:
        """
        Extracts JSON with regex.
        Args:
            response (str): The response from the LLM.
            context_json (Dict[str, Any], optional): The context JSON. Default is None.
        Returns:
            Union[Dict[str, Any], str]: The extracted JSON or the response if no JSON is found.
        """
        line_re = re.compile(r"\{[^\}]+\}")
        records = line_re.findall(response)
        try:
            if not records:
                raise CustomError(
                    "Failed to create JSON response!",
                    status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            final_dict = await convert_text_to_dict(records[0])
            if context_json:
                final_dict["Chunks"] = context_json
            return final_dict
        except Exception as e:
            logging.exception(f"Not able to extract JSON with regex. Error: {e}")
            raise CustomError(
                f"Failed to create JSON response! Error: {e}",
                status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
