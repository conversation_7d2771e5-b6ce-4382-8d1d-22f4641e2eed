from langchain.docstore.document import Document
from typing import List


class RecursiveWordTextSplitter:
    """
    A text splitter that recursively breaks text into chunks based on word count.

    Args:
        chunk_size (int): The maximum number of words in each chunk.
        chunk_overlap (int, optional): Number of words to overlap between chunks. Defaults to 0.
        split_func (Callable, optional): Function to split text into words. Defaults to str.split.
    """

    def __init__(
        self, chunk_size: int, chunk_overlap: int = 0, split_func=lambda x: x.split()
    ):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.split_func = split_func

    async def split_text(self, text: str) -> List[str]:
        """
        Splits a single string into chunks based on word count.

        Args:
            text (str): The text to split.

        Returns:
            List[str]: A list of text chunks.
        """
        chunks = []
        words = self.split_func(text)  # Split into words
        current_chunk = []
        for word in words:
            current_chunk.append(word)
            if len(current_chunk) == self.chunk_size:
                chunks.append(" ".join(current_chunk))
                if self.chunk_overlap > 0:
                    current_chunk = current_chunk[
                        -self.chunk_overlap :
                    ]  # Keep the overlap
                else:
                    current_chunk = []
        if current_chunk:
            chunks.append(" ".join(current_chunk))
        return chunks

    async def create_documents(self, text: str) -> List[Document]:
        """
        Creates Document objects from a list of texts, splitting them into chunks based on word count.

        Args:
            texts (List[str]): A list of strings representing documents or text content.

        Returns:
            List[Document]: A list of Document objects, each containing a chunk of text.
        """
        chunks = await self.split_text(text)
        return [Document(page_content=chunk) for chunk in chunks]

    async def split_documents(
        self, documents: List[Document], chunk_label: str = "child_chunk_num"
    ) -> List[Document]:
        """
        Splits a list of Documents into smaller Documents based on word count.

        Args:
            documents (List[Document]): A list of documents to split.

        Returns:
            List[Document]: A list of split documents.
        """
        chunk_number = 1
        split_documents = []
        for doc in documents:
            text_chunks = await self.split_text(doc.page_content)
            for chunk in text_chunks:
                metadata = (
                    doc.metadata.copy()
                )  # Make a copy to avoid modifying original
                metadata[chunk_label] = chunk_number  # Add chunk number (1-based)
                split_documents.append(Document(page_content=chunk, metadata=metadata))
                chunk_number += 1
        return split_documents
