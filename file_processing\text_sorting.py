import constants
from typing import List, <PERSON>ple


async def sort_text_by_line(
    text_data: List[Tuple[List[int], str]],
) -> List[List[Tuple[List[int], str]]]:
    """
    Sort the text data by the y-center of the bounding boxes first (to handle different lines).
    Args:
        text_data (List[Tuple[List[int], str]]): The text data.
    Returns:
        List[List[Tuple[List[int], str]]]: The sorted text data.
    """
    # Sort text data by the y-center of the bounding boxes first (to handle different lines)
    text_data.sort(key=lambda x: x[0][1])

    current_line = []
    current_line_y = text_data[0][0][1]
    lines = []

    for bbox, text in text_data:
        # If the bbox is on the same line (within a small tolerance), add to current line
        if abs(bbox[1] - current_line_y) < constants.LINE_TOLERANCE:  # Tolerance value
            current_line.append((bbox, text))
        else:
            # Sort the current line by x-coordinate and add to sorted text
            current_line.sort(key=lambda x: x[0][0])
            lines.append(current_line)
            current_line = [(bbox, text)]
            current_line_y = bbox[1]

    # Sort and add the last line
    current_line.sort(key=lambda x: x[0][0])
    lines.append(current_line)
    return lines


async def find_overlapping_chunks(
    bounding_boxes: List[List[Tuple[List[int], str]]],
) -> List[List[List[Tuple[List[int], str]]]]:
    """
    Find the overlapping chunks in the bounding boxes.
    Args:
        bounding_boxes (List[List[Tuple[List[int], str]]]): The bounding boxes.
    Returns:
        List[List[List[Tuple[List[int], str]]]]: The overlapping chunks.
    """
    boxes_chunk = []

    for boxes in bounding_boxes:

        chunks = []
        used = [False] * len(boxes)

        async def overlap(box1, box2):
            return not (box1[2] <= box2[0] or box2[2] <= box1[0])

        for i, box in enumerate(boxes):
            if used[i]:
                continue
            current_chunk = [box]
            used[i] = True
            for j, other_box in enumerate(boxes):
                if i != j and not used[j] and await overlap(box[0], other_box[0]):
                    current_chunk.append(other_box)
                    used[j] = True
            chunks.append(current_chunk)

        boxes_chunk.append(chunks)

    return boxes_chunk


async def merge_text_from_chunks(
    chunks: List[List[List[Tuple[List[int], str]]]],
) -> str:
    """
    Merge the text from the chunks.
    Args:
        chunks (List[List[List[Tuple[List[int], str]]]]): The chunks.
    Returns:
        str: The merged text.
    """
    merged_text = []

    for line_chunk in chunks:

        for chunk in line_chunk:
            # Separate underscore boxes from others
            underscore_boxes = [box for box in chunk if "____" in box[1]]
            text_boxes = [box for box in chunk if "____" not in box[1]]

            # Sort text boxes by x-coordinate
            text_boxes_sorted = sorted(text_boxes, key=lambda x: x[0][0])

            # Merge text from sorted text boxes
            text_line = " ".join([box[1] for box in text_boxes_sorted])

            # Append underscore boxes' text at the end if any
            if underscore_boxes:
                underscore_text = " ".join([box[1] for box in underscore_boxes])
                text_line += " " + underscore_text

            merged_text.append(text_line)

        merged_text.append("\n")

    return " ".join(merged_text)


async def get_sorted_text(bounding_boxes: List[List[Tuple[List[int], str]]]) -> str:
    """
    Get the sorted text from the bounding boxes.
    Args:
        bounding_boxes (List[List[Tuple[List[int], str]]]): The bounding boxes.
    Returns:
        str: The sorted text.
    """
    lines = await sort_text_by_line(bounding_boxes)
    # Identify overlapping chunks
    chunks = await find_overlapping_chunks(lines)
    # Merge text from chunks
    text_sequence = await merge_text_from_chunks(chunks)
    return text_sequence
