from fastapi import APIRouter
import logging
from constants import INSTANCE_TYPE
from typing import Dict, Any

index_router = APIRouter()


@index_router.get("/")
async def index() -> Dict[str, Any]:
    """
    Index route to check if the API is working.
    Returns:
        Dict[str, Any]: The status of the API.
    """
    logging.info("Index is working")
    return {
        "Status": True,
        "message": "PDFPlus API is working..",
        "instance": INSTANCE_TYPE,
    }
