from langchain_openai.embeddings.azure import AzureOpenAIEmbeddings
from langchain_openai.embeddings.base import _process_batched_chunked_embeddings
from typing import List, Optional, Dict, Any, cast


class CustomAzureOpenAIEmbeddings(AzureOpenAIEmbeddings):

    def embed_documents(
        self, texts: List[str], chunk_size: int | None = None
    ) -> Dict[str, Any]:
        """Modified embed_documents function to return the full response.

        Args:
            texts: The list of texts to embed.
            chunk_size: The chunk size of embeddings. If None, will use the chunk size
                specified by the class.

        Returns:
            List of full responses from the API, one for each text.
        """
        chunk_size_ = chunk_size or self.chunk_size
        if not self.check_embedding_ctx_length:
            responses: List[Dict[str, Any]] = []
            for i in range(0, len(texts), chunk_size_):
                response = self.client.create(
                    input=texts[i : i + chunk_size_], **self._invocation_params
                )
                if not isinstance(response, dict):
                    response = response.model_dump()
                responses.append(response)

            return {
                "embeddings": [r["embedding"] for r in responses[0]["data"]],
                "model": responses[0]["model"],
                "usage": responses[0]["usage"],
            }

        # If length-safe embedding is needed, you may need to modify this part as well
        engine = cast(str, self.deployment)
        return self._get_len_safe_embeddings(texts, engine=engine)

    def embed_query(self, text: str) -> Dict[str, Any]:
        """Call out to OpenAI's embedding endpoint for embedding query text.

        Args:
            text: The text to embed.

        Returns:
            Embedding and usage for the text.
        """
        return self.embed_documents([text])

    def _get_len_safe_embeddings(
        self, texts: List[str], *, engine: str, chunk_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate length-safe embeddings for a list of texts.

        This method handles tokenization and embedding generation, respecting the
        set embedding context length and chunk size. It supports both tiktoken
        and HuggingFace tokenizer based on the tiktoken_enabled flag.

        Args:
            texts (List[str]): A list of texts to embed.
            engine (str): The engine or model to use for embeddings.
            chunk_size (Optional[int]): The size of chunks for processing embeddings.

        Returns:
            Dict[str, Any]: A list of full responses for each batch of tokens.
        """
        _chunk_size = chunk_size or self.chunk_size
        _iter, tokens, indices = self._tokenize(texts, _chunk_size)
        batched_responses: List[Dict[str, Any]] = []
        for i in _iter:
            response = self.client.create(
                input=tokens[i : i + _chunk_size], **self._invocation_params
            )
            if not isinstance(response, dict):
                response = response.model_dump()
            batched_responses.append(response)

        # Process the embeddings from the full responses if needed
        embeddings = _process_batched_chunked_embeddings(
            len(texts),
            tokens,
            [r["embedding"] for r in batched_responses[0]["data"]],
            indices,
            self.skip_empty,
        )
        _cached_empty_embedding: Optional[List[float]] = None

        def empty_embedding() -> List[float]:
            nonlocal _cached_empty_embedding
            if _cached_empty_embedding is None:
                average_embedded = self.client.create(
                    input="", **self._invocation_params
                )
                if not isinstance(average_embedded, dict):
                    average_embedded = average_embedded.model_dump()
                _cached_empty_embedding = average_embedded["data"][0]["embedding"]
            return _cached_empty_embedding

        # Replace None embeddings with empty embeddings if needed
        processed_embeddings = [
            e if e is not None else empty_embedding() for e in embeddings
        ]

        return {
            "embeddings": processed_embeddings,
            "model": batched_responses[0]["model"],
            "usage": batched_responses[0]["usage"],
        }

    async def _aget_len_safe_embeddings(
        self, texts: List[str], *, engine: str, chunk_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Asynchronously generate length-safe embeddings for a list of texts.

        This method handles tokenization and asynchronous embedding generation,
        respecting the set embedding context length and chunk size. It supports both
        `tiktoken` and HuggingFace `tokenizer` based on the tiktoken_enabled flag.

        Args:
            texts (List[str]): A list of texts to embed.
            engine (str): The engine or model to use for embeddings.
            chunk_size (Optional[int]): The size of chunks for processing embeddings.

        Returns:
            Dict[str, Any]: A list of full responses for each batch of tokens.
        """

        _chunk_size = chunk_size or self.chunk_size
        _iter, tokens, indices = self._tokenize(texts, _chunk_size)
        batched_responses: List[Dict[str, Any]] = []
        _chunk_size = chunk_size or self.chunk_size
        for i in range(0, len(tokens), _chunk_size):
            response = await self.async_client.create(
                input=tokens[i : i + _chunk_size], **self._invocation_params
            )
            if not isinstance(response, dict):
                response = response.model_dump()
            batched_responses.append(response)

        embeddings = _process_batched_chunked_embeddings(
            len(texts),
            tokens,
            [r["embedding"] for r in batched_responses[0]["data"]],
            indices,
            self.skip_empty,
        )
        _cached_empty_embedding: Optional[List[float]] = None

        async def empty_embedding() -> List[float]:
            nonlocal _cached_empty_embedding
            if _cached_empty_embedding is None:
                average_embedded = await self.async_client.create(
                    input="", **self._invocation_params
                )
                if not isinstance(average_embedded, dict):
                    average_embedded = average_embedded.model_dump()
                _cached_empty_embedding = average_embedded["data"][0]["embedding"]
            return _cached_empty_embedding

        # Replace None embeddings with empty embeddings if needed
        processed_embeddings = [
            e if e is not None else await empty_embedding() for e in embeddings
        ]

        return {
            "embeddings": processed_embeddings,
            "model": batched_responses[0]["model"],
            "usage": batched_responses[0]["usage"],
        }

    async def aembed_documents(
        self, texts: List[str], chunk_size: int | None = None
    ) -> Dict[str, Any]:
        """Call out to OpenAI's embedding endpoint async for embedding search docs.

        Args:
            texts: The list of texts to embed.
            chunk_size: The chunk size of embeddings. If None, will use the chunk size
                specified by the class.

        Returns:
            List of full responses from the API, one for each text.
        """
        chunk_size_ = chunk_size or self.chunk_size
        if not self.check_embedding_ctx_length:
            responses: List[Dict[str, Any]] = []
            for i in range(0, len(texts), chunk_size_):
                response = await self.async_client.create(
                    input=texts[i : i + chunk_size_], **self._invocation_params
                )
                if not isinstance(response, dict):
                    response = response.model_dump()
                responses.append(response)
            return {
                "embeddings": [r["embedding"] for r in responses[0]["data"]],
                "model": responses[0]["model"],
                "usage": responses[0]["usage"],
            }

        engine = cast(str, self.deployment)
        return await self._aget_len_safe_embeddings(texts, engine=engine)

    async def aembed_query(self, text: str) -> Dict[str, Any]:
        """Call out to OpenAI's embedding endpoint async for embedding query text.

        Args:
            text: The text to embed.

        Returns:
            Embedding for the text.
        """
        return await self.aembed_documents([text])
